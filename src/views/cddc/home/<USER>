<template>
  <div class="home-container">
    <!-- 我的项目 -->
    <div class="section">
      <ProjectSelector
        @change="handleProjectChange"
        @userInfo="handleUserInfo"
        @pList="handleProjectList"
        @workstationInfo="handleWorkstationInfo"
        :showProjcetInfo="false"
      >
      <template #rightControls>
        <WorkstationQrcode
          :workstation-info="workStation"
          :user-info="userInfo"
          :current-project="currentProject"
        />
      </template>
      </ProjectSelector>
    </div>

    <!-- 用户信息区域 -->
    <div class="user-box">
      <div class="user-info">
        <div class="avatar">
          <a-image :src="headerImg" alt="用户头像" />
          {{ userInfo?.name || '' }}
        </div>
        <div class="info">
          <h2>Hi, {{ userInfo?.name || '' }} 欢迎登录{{ title||"智工育匠" }}</h2>
          <div class="time-display">
            <div class="date">{{ dateDisplay }}</div>
            <div class="time">
              <span class="time-unit">{{ timeDisplay.hours }}</span>
              <span class="time-separator">:</span>
              <span class="time-unit">{{ timeDisplay.minutes }}</span>
              <span class="time-separator">:</span>
              <span class="time-unit">{{ timeDisplay.seconds }}</span>
            </div>
          </div>
        </div>
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button type="primary" size="large" @click="startTraining" :disabled="isStart">
            <template #icon>
              <PlayCircleOutlined />
            </template>
            去训练</a-button
          >
          <a-button
            type="primary"
            size="large"
            @click="startAssessment"
            :disabled="!isToBeCheck || !canExamine"
          >
            <template #icon>
              <TrophyOutlined />
            </template>
            技能考核</a-button
          >
          <a-button
            type="primary"
            size="large"
            @click="startCompetition"
            :disabled="!isToBeCheck || !canCompetition"
          >
            <template #icon>
              <CrownOutlined />
            </template>
            技能比赛</a-button
          >
          <a-button size="large" @click="viewReport" :disabled="isStart">
            <template #icon>
              <EyeOutlined />
            </template>
            查看报告</a-button
          >
          <a-button size="large" @click="startLearning" :disabled="isStart">
            <template #icon>
              <VideoCameraOutlined />
            </template>
            课程学习</a-button
          >
        </div>
      </div>
    </div>
    <!-- 数据统计 -->
    <div class="home-data" v-if="recordVal">
      <StatisticsReport class="flex-1 w-0" :record-id="recordVal" :is-client="true" />
      <ProgressReport class="flex-1 w-0" :record-id="recordVal" :is-client="true" />
      <LatestTrainReport class="flex-1 w-0" :record-id="recordVal" :is-client="true" />
    </div>
    <ErrorSoundSelector></ErrorSoundSelector>

    <!-- 视频学习弹框 -->
    <BasicModal
      @register="register"
      title="课程学习"
      :minHeight="400"
      width="42vw"
      centered
      destroyOnClose
      :showCancelBtn="false"
      :maskClosable="false"
      ok-text="关闭"
      @ok="openModal(false)"
    >
      <VideoPlayer v-if="videoUrl" :src="videoUrl" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import headerImg from '/@/assets/images/header.svg';
import { getCurrentDate } from '/@/api/location/home';
import {
  PlayCircleOutlined,
  EyeOutlined,
  TrophyOutlined,
  CrownOutlined,
  VideoCameraOutlined,
} from '@geega-ui-plus/icons-vue';
import { createLocalStorage } from '/@/utils/cache';
import ProjectSelector from '/@/components/MyProject/ProjectSelector.vue';
import StatisticsReport from '/@/views/cddc/report/components/StatisticsReport.vue';
import ProgressReport from '/@/views/cddc/report/components/ProgressReport.vue';
import LatestTrainReport from '/@/views/cddc/report/components/ProjectRank.vue';
import { V1ManageTranProjectWithName, V1ManageProcessAlgorithmWithCode } from '/@/api/cddc.req';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import VideoPlayer from '/@/components/VideoPlayer/index.vue';
import WorkstationQrcode from '/@/components/WorkstationQrcode/index.vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import ErrorSoundSelector from '/@/components/ErrorSoundSelector.vue';
const router = useRouter();
const dateDisplay = ref('');
const timeDisplay = ref({
  hours: '00',
  minutes: '00',
  seconds: '00',
});
const userId = ref<string>('');
// 项目列表
const projectList = ref<any[]>([]);
// 选择项目
const recordVal = ref<string>();

const userInfo = ref<Record<string, any>>({});
const workStation = ref<Record<string, any>>({});

const {title}=useGlobSetting();

let serverTimeDiff = 0; // 存储本地时间与服务器时间的差值
const isExamine = ref<any>();
const isCompetition = ref<any>();

// 更新时间
let timer: NodeJS.Timer;
const updateTime = () => {
  // 使用本地时间加上与服务器的时间差来更新显示时间
  const now = Date.now() + serverTimeDiff;
  const date = dayjs(now);
  dateDisplay.value = date.format('YYYY年MM月DD日');
  timeDisplay.value = {
    hours: date.format('HH'),
    minutes: date.format('mm'),
    seconds: date.format('ss'),
  };
};

// 按钮事件处理
const startTraining = async () => {
  const ls = createLocalStorage();
  const currentProject = projectList.value.find((item) => item.recordId === recordVal.value);
  ls.set('currProjectId', currentProject?.projectId);
  ls.set('currRecordId', recordVal.value);
  router.push({
    path: '/cddc/training',
    query: { projectId: currentProject?.projectId, origin: 'home' },
  });
};

const viewReport = () => {
  router.push({ path: '/cddc/report', query: { recordId: recordVal.value, origin: 'home' } });
};

// 技能考核
const startAssessment = async () => {
  router.push({
    path: '/cddc/skill-assessment',
    query: { projectId: isExamine.value.id, origin: 'home' },
  });
};

// 技能比赛
const startCompetition = async () => {
  router.push({
    path: '/cddc/skill-competition',
    query: { projectId: isCompetition.value.id, origin: 'home' },
  });
};

// 获取服务器时间并计算时间差
const initServerTime = async () => {
  try {
    const res = await getCurrentDate();
    const serverTime = dayjs(res.result).valueOf(); // 转换为时间戳
    const localTime = Date.now();
    serverTimeDiff = serverTime - localTime; // 计算时间差
    updateTime(); // 立即更新一次时间显示

    // 启动定时器，每秒更新时间
    timer = setInterval(updateTime, 1000);
  } catch (error) {
    console.error('Failed to get server time:', error);
    // 如果获取服务器时间失败，使用本地时间作为备选
    serverTimeDiff = 0;
    updateTime();
    timer = setInterval(updateTime, 1000);
  }
};

// 项目列表
const handleProjectList = (list: any[]) => {
  projectList.value = list;
};

const { trainingUrl } = useGlobSetting();
const [register, { openModal }] = useModal();
const videoUrl = ref('');
const currentProject = ref<any>(null);

// 课程学习
const startLearning = async () => {
  if (!currentProject.value?.countAlgorithm) {
    message.warning('当前项目未配置学习视频');
    return;
  }

  // 如果已经有视频URL，直接使用
  if (currentProject.value?.videoUrl) {
    videoUrl.value = currentProject.value.videoUrl;
    openModal();
    return;
  }

  // 如果没有视频URL，则重新获取
  try {
    const videoInfo = await V1ManageProcessAlgorithmWithCode({
      code: currentProject.value.countAlgorithm,
    });
    const fetchedVideoUrl = videoInfo?.videoUrl || trainingUrl || '/example.mp4';
    videoUrl.value = fetchedVideoUrl;
    currentProject.value.videoUrl = fetchedVideoUrl; // 缓存到项目对象中
    openModal();
  } catch (error) {
    message.error('获取视频信息失败，请重试');
  }
};

// 项目切换处理
const handleProjectChange = async (project: any) => {
  if (!project) return;
  currentProject.value = project;
  recordVal.value = project.recordId;

  // 获取项目的学习视频URL
  if (project.countAlgorithm) {
    try {
      const videoInfo = await V1ManageProcessAlgorithmWithCode({
        code: project.countAlgorithm,
      });
      currentProject.value.videoUrl = videoInfo?.videoUrl || trainingUrl || '/example.mp4';
    } catch (error) {
      console.error('获取视频信息失败:', error);
      currentProject.value.videoUrl = trainingUrl || '/example.mp4';
    }
  }

  if (isToBeCheck.value) {
    isExamine.value = await handleCheckProject(project.name, 2); // 查询考核项目
    isCompetition.value = await handleCheckProject(project.name, 3); // 查询比赛项目
  }
};

const handleUserInfo = (info: any) => {
  userInfo.value = info;
  userId.value = info.id;
};

const handleWorkstationInfo = (info: any) => {
  workStation.value = info;
};

// 开始训练是否能用
const isStart = computed(() => {
  return !userId.value || !projectList.value?.length;
});

// 是否为待考核状态
const isToBeCheck = computed(() => {
  const currentProject = projectList.value.find((item) => item.recordId === recordVal.value);
  const allowedStatuses = ['TO_BE_CHECK', 'CHECKED', 'FAILED_CHECK', 'PASSED_CHECK', 'COMPLETED'];
  return allowedStatuses.includes(currentProject?.status);
});

// 查询是否有考核、比赛项目
const handleCheckProject = async (name: string, type: number) => {
  const res = await V1ManageTranProjectWithName({
    name,
    type,
  });
  return res;
};

// 是否可以考核
const canExamine = computed(() => {
  return isExamine.value && isExamine.value.status !== 'DISABLE';
});

// 是否可以比赛
const canCompetition = computed(() => {
  return isCompetition.value && isCompetition.value.status !== 'DISABLE';
});

onMounted(() => {
  initServerTime(); // 初始化服务器时间
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style lang="less" scoped>
@import './style/home-components.less';

.home-container,
.dark-ignore-image {
  color: #ffffff;
  background: #040405 url('@/assets/images/screen/page-bg.png') no-repeat center / 100% 100%;
  height: calc(100vh - 50px); // 减去页头高度，50px约等于2.6vw
  display: flex;
  flex-direction: column;

  .user-box {
    background-image: url('@/assets/images/screen/home-bg.png');
    background-size: cover; // 使用cover保持宽高比，避免拉伸
    background-position: center;
    background-repeat: no-repeat;
    height: 35vh; // 调整为更合适的高度，考虑到pad尺寸和50px页头
    max-height: 40vh;
    display: flex;
    flex: 1;
    align-items: center;
  }

  .user-info {
    display: flex;
    color: #ffffff;
    flex-direction: column;
    justify-content: center;
    border-radius: 0.2vw;
    margin-left: 8vw; // 适当减小左边距

    .avatar {
      display: flex;
      align-items: center;
      font-size: 1.8vw; // 适当减小字体大小
      gap: 1vw;

      :deep(.cddc-ant-image) {
        width: 3.5vw; // 适当减小头像尺寸
        height: 3.5vw;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .info {
      h2 {
        color: #ffffff;
        font-size: 1.2vw;
        font-weight: 500;
        margin: 1.2vh 0;
      }

      .time-display {
        .date {
          font-size: 1.2vw;
          margin: 0;
          color: #ffffff;
        }

        .time {
          display: flex;
          align-items: center;
          margin-top: 1.2vh;
          font-family: 'Digital-7', Arial, sans-serif;

          .time-unit {
            font-size: 1.8vw;
            color: #ffffff;
            background: rgba(0, 0, 0, 0.3) url('@/assets/svg/screen/home-time.svg') no-repeat center /
              100% 100%;
            border-radius: 0.2vw;
            min-width: 3vw;
            text-align: center;
            display: inline-block;
          }

          .time-separator {
            font-size: 1.8vw;
            color: #ffffff;
            margin: 0 0.2vw;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 1.5vw;
      margin-top: 3vh;

      .cddc-ant-btn {
        min-width: 7vw;
        height: 5.5vh;
        font-size: 1.85vw;
        padding: 0 1vw;
        &:hover,
        &:focus {
          background-color: rgb(30, 30, 30);
          border-color: rgb(59, 59, 59);
        }
      }
    }
  }

  .home-data {
    padding: 0 2vw 2vh;
    min-height: 0; // 确保flex布局正常工作
    display: flex;
    gap: 1vw;
    --ignore-dark--image: linear-gradient(180deg, #040b1b 0%, rgba(12, 14, 16, 0) 100%);
    background: var(--ignore-dark--image);

    :deep(.statistics-report),
    :deep(.progress-report),
    :deep(.project-rank) {
      flex: 1;
      width: 0; // 确保三等分
      height: 100%;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 0.4vw;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.2vw;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2vh 2vw;
    --ignore-dark-color: #0a0d10;
    background-color: var(--ignore-dark-color);

    .section-title {
      font-size: 1vw;
      font-weight: 500;
    }

    .location-info {
      color: #d7d7d7;
    }
  }

  .project-card {
    display: inline-flex;
    align-items: center;
    gap: 0.8vw;
    border-radius: 0.2vw;

    .project-name {
      font-size: 1vw;
      font-weight: 500;
    }
  }
}
.cddc-ant-btn-primary[disabled], .cddc-ant-btn-primary[disabled]:hover, .cddc-ant-btn-primary[disabled]:focus, .cddc-ant-btn-primary[disabled]:active{
  color:rgba(221, 221, 221,0.5);
}
// 视频样式已移至 VideoPlayer 组件

// 确保在较小屏幕上也能正常显示
@media screen and (max-width: 1024px) {
  .home-container {
    .user-box {
      height: 30vh;
    }

    .user-info {
      margin-left: 5vw;

      .avatar {
        font-size: 1.6vw;

        :deep(.cddc-ant-image) {
          width: 3vw;
          height: 3vw;
        }
      }

      .info {
        h2 {
          font-size: 1.1vw;
        }

        .time-display {
          .date {
            font-size: 1.1vw;
          }

          .time {
            .time-unit,
            .time-separator {
              font-size: 1.6vw;
            }
          }
        }
      }

      .action-buttons {
        .cddc-ant-btn {
          min-width: 6vw;
          height: 3.5vh;
          font-size: 0.9vw;
        }
      }
    }

    .home-data {
      padding: 0 1.5vw 1.5vh;
      gap: 0.8vw;
    }
  }
}

// user-box背景图适配优化 - 基于设计稿2160*1440的宽高比1.5
@media screen and (max-aspect-ratio: 3/2) {
  .user-box {
    background-size: 100% auto; // 宽屏时优先适配宽度
  }
}

@media screen and (min-aspect-ratio: 3/2) {
  .user-box {
    background-size: auto 100%; // 高屏时优先适配高度
  }
}

// Mac Pro 13寸适配 (2560*1600, 宽高比1.6)
@media screen and (min-width: 2560px) and (max-width: 2560px) and (min-height: 1600px) and (max-height: 1600px) {
  .user-box {
    background-size: cover; // Mac Pro 13寸使用cover模式
    background-position: center center;
  }
}

// MacBook Pro 13寸通用适配 (包括各种分辨率)
@media screen and (min-width: 1280px) and (max-width: 1440px) and (min-height: 800px) and (max-height: 900px),
       screen and (min-width: 1440px) and (max-width: 1680px) and (min-height: 900px) and (max-height: 1050px) {
  .user-box {
    background-size: cover;
    background-position: center center;
  }

  // 针对MacBook的用户信息区域优化
  .user-info {
    margin-left: 6vw; // 适当调整左边距

    .avatar {
      font-size: 1.6vw;

      :deep(.cddc-ant-image) {
        width: 3.2vw;
        height: 3.2vw;
      }
    }

    .info {
      h2 {
        font-size: 1.1vw;
      }

      .time-display {
        .date {
          font-size: 1.1vw;
        }

        .time {
          .time-unit {
            font-size: 1.6vw;
            min-width: 2.8vw;
          }

          .time-separator {
            font-size: 1.6vw;
          }
        }
      }
    }

    .action-buttons {
      gap: 1.2vw;

      .cddc-ant-btn {
        min-width: 6.5vw;
        height: 4.5vh;
        font-size: 1.0vw;
      }
    }
  }
}

// 更小屏幕的适配
@media screen and (max-width: 768px) {
  .home-container {
    height: calc(100vh - 3.5vw); // 小屏幕时页头相对更大
  }

  .user-box {
    background-size: cover; // 小屏幕保持cover模式
  }

  .home-container {
    .user-info {
      margin-left: 3vw;

      .avatar {
        font-size: 2.2vw;
        gap: 1.2vw;

        :deep(.cddc-ant-image) {
          width: 4vw;
          height: 4vw;
        }
      }

      .info {
        h2 {
          font-size: max(14px, 1.5vw);
        }

        .time-display {
          .date {
            font-size: max(12px, 1.4vw);
          }

          .time {
            .time-unit {
              font-size: max(16px, 2.2vw);
              min-width: 3.5vw;
            }

            .time-separator {
              font-size: max(16px, 2.2vw);
            }
          }
        }
      }

      .action-buttons {
        gap: 1.2vw;
        flex-wrap: wrap;

        .cddc-ant-btn {
          min-width: 8vw;
          height: 4vh;
          font-size: max(12px, 1.2vw);
          padding: 0 1.2vw;
        }
      }
    }

    .home-data {
      padding: 0 1vw 1vh;
      gap: 0.6vw;
      flex-direction: column;

      :deep(.statistics-report),
      :deep(.progress-report),
      :deep(.project-rank) {
        width: 100%;
        min-height: 25vh;
      }
    }
  }
}
</style>

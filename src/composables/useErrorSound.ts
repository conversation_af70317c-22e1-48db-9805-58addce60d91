import { ref, readonly } from 'vue';

export type ErrorType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 21 | 22 | 23 | 31 | 32 | 33 | 41 | 42 | 43 | 51 | 52;
export type SoundType = 'rational' | 'lively';

interface ErrorSound {
  type: ErrorType;
  name: string;
  description: string;
  errorMessage: string;
  hasSound: boolean;
  files?: {
    rational: string;
    lively: string;
  };
}

const ERROR_SOUNDS: Record<ErrorType, ErrorSound> = {
  // 基础作业检测
  1: {
    type: 1,
    name: 'BOTH_HANDS',
    description: '双手持枪',
    errorMessage: '未双手作业',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/tightening/both-hands.mp3',
      lively: '/resource/sounds/lively/tightening/both-hands.mp3'
    }
  },
  2: {
    type: 2,
    name: 'VERTICAL_WORKING_SURFACE',
    description: '垂直作业面',
    errorMessage: '未垂直作业面',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/tightening/vertical-surface.mp3',
      lively: '/resource/sounds/lively/tightening/vertical-surface.mp3'
    }
  },
  3: {
    type: 3,
    name: 'TIGHTEN_FIT',
    description: '拧紧贴合',
    errorMessage: '拧紧未贴合',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/tightening/tighten-fit.mp3',
      lively: '/resource/sounds/lively/tightening/tighten-fit.mp3'
    }
  },
  4: {
    type: 4,
    name: 'GREEN_LIGHT',
    description: '扳手绿灯',
    errorMessage: '拧紧枪未亮绿灯',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/tightening/green-light.mp3',
      lively: '/resource/sounds/lively/tightening/green-light.mp3'
    }
  },
  5: { type: 5, name: 'COUNT_PERSON', description: '人员检测', errorMessage: '', hasSound: false },
  6: { type: 6, name: 'LIGHT_FLAG', description: '灯标识', errorMessage: '', hasSound: false },
  7: { type: 7, name: 'GLOBAL_RESULT', description: '全局作业结果', errorMessage: '', hasSound: false },
  8: {
    type: 8,
    name: 'WEAR_GLOVES',
    description: '手套识别',
    errorMessage: '未戴手套',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/wear-gloves.mp3',
      lively: '/resource/sounds/lively/wear-gloves.mp3'
    }
  },

  // 堵盖相关检测
  21: {
    type: 21,
    name: 'C_SIZE',
    description: '堵盖大小判定',
    errorMessage: '堵盖选择错误',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/cap/size-error.mp3',
      lively: '/resource/sounds/lively/cap/size-error.mp3'
    }
  },
  22: {
    type: 22,
    name: 'C_FIT_JUDGE',
    description: '堵盖贴合判定',
    errorMessage: '堵盖不贴合',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/cap/fit-error.mp3',
      lively: '/resource/sounds/lively/cap/fit-error.mp3'
    }
  },
  23: {
    type: 23,
    name: 'C_PRESS',
    description: '堵盖手部按压判定',
    errorMessage: '堵盖未按压',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/cap/press-error.mp3',
      lively: '/resource/sounds/lively/cap/press-error.mp3'
    }
  },

  // 胶条相关检测
  31: {
    type: 31,
    name: 'SR_INSTALL',
    description: '胶条安装判定',
    errorMessage: '安装不规范',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/seal/install-error.mp3',
      lively: '/resource/sounds/lively/seal/install-error.mp3'
    }
  },
  32: {
    type: 32,
    name: 'SR_PRESS',
    description: '胶条手部按压判定',
    errorMessage: '按压不规范',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/seal/press-error.mp3',
      lively: '/resource/sounds/lively/seal/press-error.mp3'
    }
  },
  33: {
    type: 33,
    name: 'SR_FIT_JUDGE',
    description: '胶条结果判定',
    errorMessage: '安装不贴合',
    hasSound: true,
    files: {
      rational: '/resource/sounds/安装未贴合（理性）.mp3',
      lively: '/resource/sounds/安装未贴合（活泼）.mp3'
    }
  },

  // 管线相关检测
  41: {
    type: 41,
    name: 'PI_OIL_DISTANCE',
    description: '油管距离判定',
    errorMessage: '油管未闭合',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/pipeline/oil-distance.mp3',
      lively: '/resource/sounds/lively/pipeline/oil-distance.mp3'
    }
  },
  42: {
    type: 42,
    name: 'PI_PIPING_DISTANCE',
    description: '管路距离判定',
    errorMessage: '插拔管未闭合',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/pipeline/piping-distance.mp3',
      lively: '/resource/sounds/lively/pipeline/piping-distance.mp3'
    }
  },
  43: {
    type: 43,
    name: 'PI_HAND_BACK',
    description: '管线-手部回拔判定',
    errorMessage: '安装完成未回拔',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/pipeline/hand-back.mp3',
      lively: '/resource/sounds/lively/pipeline/hand-back.mp3'
    }
  },

  // 线束插接相关检测
  51: {
    type: 51,
    name: 'HC_HAND_BACK',
    description: '线束插接-手部回拔判定',
    errorMessage: '安装完成未回拔',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/harness/hand-back.mp3',
      lively: '/resource/sounds/lively/harness/hand-back.mp3'
    }
  },
  52: {
    type: 52,
    name: 'HC_INSTALL',
    description: '线束插接-安装结果',
    errorMessage: '安装不规范',
    hasSound: true,
    files: {
      rational: '/resource/sounds/rational/harness/install-error.mp3',
      lively: '/resource/sounds/lively/harness/install-error.mp3'
    }
  },
};

const MORE_ERROR_SOUNDS = {
  rational: '/resource/sounds/rational/more-error.mp3',
  lively: '/resource/sounds/lively/more-error.mp3'
};

export const useErrorSound = () => {
  const audioElements = ref<Map<string, HTMLAudioElement>>(new Map());
  const currentPlayingQueue = ref<string[]>([]); // 当前播放队列
  const currentPlayingIndex = ref<number>(0); // 当前播放索引
  const isPlaying = ref<boolean>(false); // 是否正在播放
  const lastPlayTime = ref<number>(0); // 上次播放时间

  // 初始化音频元素
  const initAudioElements = (soundType: SoundType = 'rational') => {
    // 清空现有音频元素
    audioElements.value.clear();

    // 创建单个错误的音频元素（只为有声音的错误类型创建）
    Object.values(ERROR_SOUNDS).forEach((sound) => {
      if (sound.hasSound && sound.files) {
        const audioFile = sound.files[soundType];
        const audio = new Audio(audioFile);
        audio.preload = 'auto';
        audioElements.value.set(audioFile, audio);
      }
    });

    // 创建多个错误的音频元素（保留以备后用）
    const moreErrorFile = MORE_ERROR_SOUNDS[soundType];
    const moreErrorAudio = new Audio(moreErrorFile);
    moreErrorAudio.preload = 'auto';
    audioElements.value.set(moreErrorFile, moreErrorAudio);
  };

  // 停止当前播放
  const stopCurrentPlayback = () => {
    // 停止所有正在播放的音频
    audioElements.value.forEach((audio) => {
      if (!audio.paused) {
        audio.pause();
        audio.currentTime = 0;
      }
    });

    // 重置播放状态
    isPlaying.value = false;
    currentPlayingQueue.value = [];
    currentPlayingIndex.value = 0;
  };

  // 播放队列中的下一个音频
  const playNextInQueue = () => {
    if (currentPlayingIndex.value >= currentPlayingQueue.value.length) {
      // 队列播放完毕
      isPlaying.value = false;
      currentPlayingQueue.value = [];
      currentPlayingIndex.value = 0;
      return;
    }

    const audioFile = currentPlayingQueue.value[currentPlayingIndex.value];
    const audio = audioElements.value.get(audioFile);

    if (audio) {
      // 设置播放结束后的回调
      const onEnded = () => {
        audio.removeEventListener('ended', onEnded);
        currentPlayingIndex.value++;
        playNextInQueue();
      };

      audio.addEventListener('ended', onEnded);

      audio.play().catch((error) => {
        console.warn(`播放错误提示音失败:`, error);
        // 播放失败时继续下一个
        currentPlayingIndex.value++;
        playNextInQueue();
      });
    } else {
      // 音频元素不存在，跳过
      currentPlayingIndex.value++;
      playNextInQueue();
    }
  };

  // 播放错误提示音
  const playErrorSound = (actionLabels?: { actionType: ErrorType }[], soundType: SoundType = 'rational') => {
    if (!actionLabels?.length) return;

    // 无论何时有新的错误推送，都停止当前播放
    if (isPlaying.value) {
      console.log('检测到新的错误推送，停止当前播放，使用最新错误列表');
      stopCurrentPlayback();
    }

    lastPlayTime.value = Date.now();

    // 确保音频元素已初始化
    if (audioElements.value.size === 0) {
      initAudioElements(soundType);
    }

    // 过滤出有声音的错误类型
    const soundableErrors = actionLabels.filter(label => {
      const errorSound = ERROR_SOUNDS[label.actionType];
      return errorSound?.hasSound && errorSound.files;
    });

    // 如果没有需要播放声音的错误，直接返回
    if (soundableErrors.length === 0) return;

    // 单个错误时，直接播放
    if (soundableErrors.length === 1) {
      const errorSound = ERROR_SOUNDS[soundableErrors[0].actionType];
      if (errorSound?.hasSound && errorSound.files) {
        const audioFile = errorSound.files[soundType];
        const audio = audioElements.value.get(audioFile);

        if (audio) {
          isPlaying.value = true;

          // 设置播放结束回调
          const onEnded = () => {
            audio.removeEventListener('ended', onEnded);
            isPlaying.value = false;
          };
          audio.addEventListener('ended', onEnded);

          audio.play().catch((error) => {
            console.warn(`播放错误提示音(${errorSound.description})失败:`, error);
            isPlaying.value = false;
          });
        }
      }
      return;
    }

    // 多个错误时，依次播放所有错误
    const audioFiles = soundableErrors
      .map(error => {
        const errorSound = ERROR_SOUNDS[error.actionType];
        return errorSound?.files?.[soundType];
      })
      .filter(Boolean) as string[];

    if (audioFiles.length > 0) {
      currentPlayingQueue.value = audioFiles;
      currentPlayingIndex.value = 0;
      isPlaying.value = true;
      playNextInQueue();
    }
  };

  // 切换声音类型（重新初始化音频元素）
  const switchSoundType = (soundType: SoundType) => {
    // 停止当前播放
    stopCurrentPlayback();
    // 重新初始化音频元素
    initAudioElements(soundType);
  };

  // 获取错误信息
  const getErrorInfo = (errorType: ErrorType) => {
    return ERROR_SOUNDS[errorType];
  };

  // 获取所有错误类型
  const getAllErrorTypes = () => {
    return Object.keys(ERROR_SOUNDS).map(Number) as ErrorType[];
  };

  // 获取有声音的错误类型
  const getSoundableErrorTypes = () => {
    return Object.values(ERROR_SOUNDS)
      .filter(sound => sound.hasSound)
      .map(sound => sound.type);
  };

  // 获取声音文件路径
  const getSoundFile = (errorType: ErrorType, soundType: SoundType) => {
    const errorSound = ERROR_SOUNDS[errorType];
    return errorSound?.files?.[soundType];
  };

  return {
    playErrorSound,
    switchSoundType,
    stopCurrentPlayback,
    getErrorInfo,
    getAllErrorTypes,
    getSoundableErrorTypes,
    getSoundFile,
    ERROR_SOUNDS,
    // 播放状态（只读）
    isPlaying: readonly(isPlaying),
  };
};
